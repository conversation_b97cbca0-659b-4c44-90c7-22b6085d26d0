'use strict';
define(function (require, exports, module) {
  exports.init = function (opt, html) {
    layui.use(['form', 'layedit', 'laydate', 'trasen', 'upload', 'treeSelect'], function () {
      var trasenTable = null;
      var form = layui.form,
        laydate = layui.laydate,
        trasen = layui.trasen,
        upload = layui.upload;

      function findIndex(arr, value, key) {
        var index = -1;
        for (var i = 0; i < arr.length; i++) {
          if (arr[i][key] == value) {
            index = i;
          }
        }
        return index;
      }

      //设置选中
      function setSelectedProcessHtml() {
        var htmlStr = ''
        var data = {
          1: '在办',
          2: '办结'
        };
        for(var i = 0; i < selProcss.length; i++) {
          htmlStr += 
            `<div class="process-item">
              <span class="process-item-info" row-id="${selProcss[i].wfInstanceId}">
                ${selProcss[i].workflowName}
                - ${selProcss[i].createDate}
                【${data[selProcss[i].status]}】
              </span>
              <i class="delete-btn oaicon oa-icon-cuowu"></i>
            </div>`
        }
        $('.process-dialog #allProcessNum').html(selProcss.length);
        $('.process-dialog .value-box #select_ProcessBox').html(htmlStr);
      }

      var selProcss = [];
      selProcss = JSON.parse(JSON.stringify(opt.data.processList));

      var wins = layer.open({
        type: 1,
        title: '选择流程',
        closeBtn: 1,
        shadeClose: false,
        area: ['800px', '600px'],
        skin: 'yourclass',
        content: html,
        success: function (layero, index) {
          common.overlayScrollbarsSet(' .scrollbar-box');

          $.each($('.process-dialog .laydate-date'), function (i, v) {
            laydate.render({
                elem: v,
                trigger: 'click',
            });
          }); 
          form.render('select');
          setSelectedProcessHtml();

          trasenTable = new $.trasenTable('grid-table-process-dialog', {
            url: '/ts-workflow/workflow/instance/getMyLaunchWorkflowList',
            datatype: 'json',
            mtype: 'get',
            multiselect: true,
            pager: 'grid-pager-process-dialog',
            shrinkToFit: true,
            sortorder: 'desc',
            sortname: 'create_date',
            postData: $.extend(
              { wfDefinitionIds: opt.data.wfDefinitionIds },
              trasen.getNamesVal($('#processQueryForm'))
            ),
            colModel: [
                {
                  label: 'ID',
                  name: 'wfInstanceId',
                  align: 'center',
                  hidden: true,
                  key: true,
                },  
                {
                    label: '流程',
                    name: 'workflowTitle',
                    sortable: false,
                    align: 'left',
                    formatter: function (cell, options, row) {
                      return `<p class="seeLink seeWf" style="cursor:pointer; white-space: normal;" handle-type="detail" row-id="${options.rowId}" row-status="${row.status}">${row.workflowTitle}</p>`
                    }
                },
                {
                  label: '发起时间',
                  sortable: true,
                  width: 120,
                  index: 'inst.create_date',
                  name: 'createDate',
                  align: 'center',
              },
              {
                label: '流程状态',
                name: 'status',
                sortable: true,
                index: 'inst.status',
                width: 60,
                align: 'center',
                formatter: function (cell, opt, row) {
                  var data = {
                    1: '在办',
                    2: '办结'
                  };
                  return data[row.status];
                },
              },
            ],
            buidQueryParams: function () {
              return $.extend(
                { wfDefinitionIds: opt.data.wfDefinitionIds },
                trasen.getNamesVal($('#processQueryForm'))
              );
            },
            gridComplete: function () {
              var ids = trasenTable.getDataIDs();
              let wfInstanceIdArr = selProcss.map(item => { return item.wfInstanceId; })
              for (var i in ids) {
                if (wfInstanceIdArr.includes(ids[i])) {
                  trasenTable.setSelection(ids[i], true);
                }
              }
            },
            onSelectAll: function (rowids, status) {
              for(var i = 0; i < rowids.length; i++) {
                var index = findIndex(selProcss, rowids[i], 'wfInstanceId');
                if (status) {
                  if (index == -1) {
                    var rowData = trasenTable.getSourceRowData(rowids[i]);
                    selProcss.push(rowData);
                  }
                } else {
                  selProcss.splice(index, 1);
                }
              }
              setSelectedProcessHtml();
            },
            onCellSelect: function (rowId, iCol, cellcontent, e) {
              var handleType = $(e.target).attr("handle-type");
              if(handleType && iCol > 1) {
                $('#grid-table-process-dialog').jqGrid("setSelection", rowId, false);
              } else {
                var index = findIndex(selProcss, rowId, 'wfInstanceId');
                if(index == -1) {
                  var rowData = trasenTable.getSourceRowData(rowId);
                  selProcss.push(rowData);
                } else {
                  selProcss.splice(index, 1);
                }
                setSelectedProcessHtml();
              }
            }
          });

          
          
          //单个删除
          $('.process-dialog')
            .off('click', '#select_ProcessBox .delete-btn')
            .on('click', '#select_ProcessBox .delete-btn', function () {
              var deleteItem = $(this).parent();
              var index = $(deleteItem).index();
              var deleteItemId = selProcss[index].wfInstanceId;
              trasenTable.setSelection(deleteItemId, false);
              selProcss.splice(index, 1);
              deleteItem.remove();
              $('.process-dialog #allProcessNum').html(selProcss.length);
            });
          
          //查看详情
          $('.process-dialog')
            .off('click', '.seeWf, .process-item-info')
            .on('click', '.seeWf, .process-item-info', function (e) {
              e.stopPropagation();
              var rowId = $(this).attr('row-id');
              var rowData = trasenTable.getSourceRowData(rowId);
              checkDetail(rowData);
            });
          
          function refresh() {
            trasenTable.refresh();
          }

          //查询流程
          form.on('submit(systemManageProcessScreenSearch)', function (data) {
            refresh()
          });

          //检索重置
          $('#process_screenReset')
            .off('click')
            .on('click', function () {
              $('#processQueryForm')[0].reset();
              form.render('select');
              refresh()
            });

          //保存
          $('.process-dialog')
            .off('click', '#save')
            .on('click', '#save', function () {
              if (opt.handClose) {
                opt.callBack && opt.callBack(selProcss, wins);
              } else {
                  opt.callBack && opt.callBack(selProcss);
                  layer.close(wins);
              }
              return false;
          });

          //清空
          $('.process-dialog')
            .off('click', '#clear')
            .on('click', '#clear', function () {
              // 获取所有选中行id
              var jqGridRowid = $('#grid-table-process-dialog').jqGrid('getGridParam', 'selarrrow');
              for (var i = 0; i < jqGridRowid.length; i++) {
                trasenTable.setSelection(jqGridRowid[i], false);
                i--;
              }
              selProcss = [];
              setSelectedProcessHtml();
          });

          //关闭选人弹窗
          $('.process-dialog')
            .off('click', '#close')
            .on('click', '#close', function () {
              layer.close(wins);
          });
        }
      });
    });
  };
});
