<template>
  <view class="form">
    <view class="form-container">
      <u-form
        class="form-box"
        ref="uForm"
        :model="form"
        :error-type="errorType"
      >
        <u-form-item
          v-for="(item, index) in formList"
          :key="index"
          :required="item.required && !disabled"
          :label-width="$formLabelWidth"
          :label-align="item.labelAlign"
          :label-position="
            item.labelPosition
              ? item.labelPosition
              : labelPositionFilter(
                  item.type,
                  $formInputLabelPosition,
                  $formTextareaLabelPosition
                )
          "
          :label="item.title"
          :prop="
            item.type == 'file' && item.baseType == 'base'
              ? item.prop
              : item.propVal
          "
        >
          <template #left>
            <text
              v-if="item.labelSlot"
              :class="item.labelSlotClass"
              :style="item.labelSlotStyle"
              @click="labelSlotClick(item)"
            >
              {{ item.labelSlot }}
            </text>
          </template>
          <template
            #right
            v-if="item.type == 'radio' || item.type == 'rate' || item.rightSlot"
          >
            <u-radio-group
              v-if="item.type == 'radio'"
              v-model="form[item.propVal]"
              :wrap="item.radioCheckWrap"
              :disabled="disabled || item.disabled"
              @change="radioGroupChange($event, item)"
            >
              <u-radio
                shape="circle"
                v-for="(radioItem, radioIndex) in item.radioList"
                :key="radioIndex"
                :name="radioItem.value"
              >
                {{ radioItem.label }}
              </u-radio>
            </u-radio-group>
            <u-rate
              v-else-if="item.type == 'rate'"
              :count="item.count"
              v-model="form[item.propVal]"
              :disabled="disabled || item.disabled"
              :inactive-color="item.inactiveColor"
              :active-color="item.activeColor"
              :size="item.size"
              :active-icon="item.activeIcon"
              :inactive-icon="item.inactiveIcon"
              :custom-prefix="item.customPrefix"
            >
            </u-rate>
            <text
              v-if="item.rightSlot"
              :class="item.rightSlotClass"
              :style="item.rightSlotStyle"
              @click="rightSlotClick(item)"
            >
              {{ item.rightSlot }}
            </text>
          </template>
          <template
            #default
            v-if="
              item.type == 'file' ||
                item.type == 'select' ||
                item.type == 'text' ||
                item.type == 'number' ||
                item.type == 'textarea'
            "
          >
            <base-upload-file
              v-if="item.type == 'file' && item.mode == 'all'"
              :show-progress="true"
              :action="item.action"
              :index="item.prop"
              :form-data="item.formData"
              :header="item.header"
              :name="item.name"
              :file-list="form[item.fileList]"
              @on-success="uploadedFile(arguments, item)"
              @on-remove="removeFile(arguments, item)"
            ></base-upload-file>
            <u-upload
              v-else-if="item.type == 'file' && item.mode == 'image'"
              max-count="9"
              width="160"
              height="160"
              :disabled-upload="disabled || item.disabledUpload"
              :show-progress="false"
              :action="item.action || '/ts-basics-bottom/fileAttachment/upload?moduleName=oa'"
              :index="item.propVal"
              :form-data="item.formData"
              :header="item.header"
              :name="item.name"
              :deletable="item.deletable"
              :file-list="form[item.prop]"
              @on-success="uploadedFile(arguments, item)"
              @on-remove="removeFile(arguments, item)"
            ></u-upload>
            <!-- :file-list="form[item.prop]" -->
            <view v-if="form[item.fileVal] && form[item.fileVal].length">
              <view
                class="file-list"
                v-for="(file, fileIndex) of form[item.fileVal]"
                :key="fileIndex"
              >
                <view class="file-list-item-title">
                  <u-icon
                    name="fujian"
                    custom-prefix="work-icon"
                    size="28"
                    color="#666"
                  ></u-icon>
                  {{ file.title || file.fkFileName }}
                </view>

                <view class="file-operation-button">
                  <text
                    class="file-operation-button-item"
                    @click="deleteFile(item, file)"
                  >
                    删除
                  </text>
                  <text
                    class="file-operation-button-item"
                    @click="previewFile(item, file)"
                  >
                    预览
                  </text>
                </view>
              </view>
            </view>
            <u-input
              v-if="item.type != 'file'"
              class="input-box"
              :class="inputClass(item)"
              :key="item.props"
              :border="$formInputBorder"
              :height="item.height"
              :type="item.type"
              :placeholder="item.placeholder"
              :disabled="disabled || item.disabled"
              :input-align="
                item.inputAlign
                  ? item.inputAlign
                  : inputAlignFilter(
                      item.type,
                      item.labelPosition,
                      $formInputAlign,
                      $formTextareaAlign
                    )
              "
              :maxlength="item.maxlength"
              v-model="form[item.propVal]"
              trim
              @input="item.callback ? changeInputVal($event, item) : ''"
              @click="
                item.type == 'select' && item.mode == 'select'
                  ? changeSelectShow(item)
                  : item.type == 'select' && item.mode == 'checkbox'
                  ? changeCheckboxShow(item)
                  : item.type == 'select' && item.mode == 'time'
                  ? changePickerShow(item)
                  : item.type == 'select' && item.mode == 'person'
                  ? choosePerson(item)
                  : item.type == 'select' && item.mode == 'dept'
                  ? chooseDept(item)
                  : ''
              "
            ></u-input>
          </template>
        </u-form-item>
      </u-form>
      <view class="button-box" v-if="showSubmitButton">
        <u-button type="primary" @click="submit">{{ submitTitle }}</u-button>
      </view>
    </view>
    <u-select
      mode="single-column"
      :list="selctAllObj[clickProp]"
      v-model="selectShow"
      @confirm="selectConfirm"
    ></u-select>
    <u-picker
      :mode="clickMode"
      v-model="pickerShow"
      :params="clickParams"
      @confirm="pickerConfirm"
    ></u-picker>
    <base-checkbox
      :list="selctAllObj[clickProp]"
      v-model="checkboxShow"
      @confirm="checkboxConfirm"
    ></base-checkbox>
  </view>
</template>

<script>
import BaseCheckbox from '@/components/base-checkbox/base-checkbox.vue';
import ajax from '@/api/ajax.js';
import BaseUploadFile from '../base-upload-file/base-upload-file.vue';
export default {
  components: { BaseCheckbox, BaseUploadFile },
  name: 'base-form',
  props: {
    //表单是否只读
    disabled: {
      type: Boolean,
      default: false
    },
    formList: {
      type: Array,
      default() {
        return [];
      }
    },
    formData: {
      type: Object,
      default() {
        return {};
      }
    },
    submitTitle: {
      type: String,
      default: '提交'
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    },
    showSubmitButton: {
      type: Boolean,
      default: true
    },
    readOnly: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      errorType: ['toast'],
      selctAllObj: {},
      clickProp: null,
      clickPropVal: null,
      relationProp: [],
      clickMode: null,
      clickParams: null,
      clickField: null,
      selectShow: false,
      pickerShow: false,
      checkboxShow: false,
      personLabel: {},
      deptLabel: {},
      form: this.formData
    };
  },
  watch: {
    formList: {
      handler(newVal) {
        this.init();
      },
      immediate: true,
      deep: true
    }
  },
  mounted() {
    //绑定验证规则，解决通过props传递变量时，微信小程序会过滤掉对象中的方法，导致自定义验证规则无效
    this.$refs.uForm.setRules(this.rules);
  },
  methods: {
    labelPositionFilter(type, inputLable, textareaLable) {
      if (type == 'textarea' || type == 'file') {
        return textareaLable;
      } else {
        return inputLable;
      }
    },
    inputAlignFilter(type, labelPosition, inputAlign, textareaAlign) {
      if (type == 'textarea') {
        return textareaAlign;
      } else {
        if (labelPosition == 'top') {
          return 'left';
        }
        return inputAlign;
      }
    },
    //初始化选择项、校验规则
    init() {
      let selctAllObj = {},
        personLabel = {},
        deptLabel = {};
      this.formList.map(item => {
        let propName = item.prop;
        if (item.optionList) {
          selctAllObj[propName] = item.optionList;
        }
        if (item.type == 'select' && item.mode == 'person') {
          personLabel[propName] = [];
        } else if (item.type == 'select' && item.mode == 'dept') {
          deptLabel[propName] = [];
        }
      });
      this.selctAllObj = JSON.parse(JSON.stringify(selctAllObj));
      this.personLabel = JSON.parse(JSON.stringify(personLabel));
      this.deptLabel = JSON.parse(JSON.stringify(deptLabel));
    },
    //单选
    radioGroupChange(e, item) {
      this.$set(this.form, item.propVal, e);
      if (item.callback) {
        item.callback(e);
      }
    },
    changeInputVal(e, item) {
      this.$nextTick(() => {
        this.$set(this.form, item.propVal, item.callback(e));
      });
    },
    //打开列选择器
    async changeSelectShow(e) {
      if (e.disabled) return;
      this.relationProp = e.relationProp || [];
      if (e.searchParams && e.searchParams.length > 0) {
        let param = {};
        for (var i = 0; i < e.searchParams.length; i++) {
          if (!this.form[e.searchParams[i].value]) {
            this.$u.toast(e.searchParams[i].message);
            return false;
          }
          param[e.searchParams[i].name] = this.form[e.searchParams[i].value];
        }
        let optionList = await this.getDatas(e, param);
        this.$set(this.selctAllObj, e.prop, optionList);
      }
      this.$nextTick(() => {
        this.selectShow = true;
        this.clickProp = e.prop;
        this.clickPropVal = e.propVal;
      });
    },
    //列选择器确认事件
    selectConfirm(e) {
      if (this.form[this.clickPropVal] !== e[0].value) {
        this.$set(this.form, this.clickProp, e[0].label);
        this.$set(this.form, this.clickPropVal, e[0].value);
        if (this.relationProp.length > 0) {
          for (var i = 0; i < this.relationProp.length; i++) {
            this.$set(this.form, this.relationProp[i].prop, '');
            if (this.relationProp[i].propVal) {
              this.$set(this.form, this.relationProp[i].propVal, '');
            }
          }
        }
      }
    },
    async getDatas(config, params) {
      let datas = await ajax.get(config.searchApi, {
        params: params,
        custom: config.searchCustom || {},
        header: config.searchHeader || {}
      });
      return datas.object.map(item => {
        return {
          label: item.name,
          value: item.id
        };
      });
    },
    //打开选择器
    changePickerShow(e) {
      if (e.disabled) return;
      this.pickerShow = true;
      this.clickMode = e.mode;
      this.clickProp = e.prop;
      this.clickParams = e.params;
      this.clickField = e.field;
    },
    //时间确认事件
    pickerConfirm(e) {
      if (this.clickMode == 'time') {
        this.$set(
          this.form,
          this.clickProp,
          this.timePickerConfirm(e, this.clickField)
        );
      }
    },
    //格式化时间
    timePickerConfirm(e, field = 'yy-MM-dd') {
      if (field == 'YY') {
        return `${e.year}年`;
      } else if (field == 'yy-MM') {
        return `${e.year}-${e.month}`;
      } else if (field == 'yy-MM-dd') {
        return `${e.year}-${e.month}-${e.day}`;
      } else if (field == 'HH:mm') {
        return `${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}`;
      } else if (field == 'yy-MM-dd HH:mm:ss') {
        return `${e.year}-${e.month}-${e.day} ${e.hour}:${e.minute}:${e.second}`;
      }
    },
    // 打开多选
    changeCheckboxShow(e) {
      if (e.disabled) return;
      let val = this.form[e.propVal].split(',');
      let optionList = e.optionList.map(item => {
        item.checked = val.some(i => i == item.value);
        return item;
      });
      this.$set(this.selctAllObj, e.prop, optionList);
      this.checkboxShow = true;
      this.clickMode = e.mode;
      this.clickProp = e.prop;
      this.clickPropVal = e.propVal;
    },
    checkboxConfirm(val, label) {
      this.$set(this.form, this.clickProp, label.join(','));
      this.$set(this.form, this.clickPropVal, val.join(','));
    },
    //选择人员
    choosePerson(item) {
      let personList = this.personLabel[item.prop];
      uni.setStorageSync('person_list', JSON.stringify(personList));
      let personPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.form[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        personPageParams = { ...personPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('personPageParams', JSON.stringify(personPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        this.$set(this.personLabel, item.prop, data);
        let personName = [],
          personId = [];
        data.map(i => {
          personName.push(i.name);
          personId.push(i.userId);
        });
        this.$set(this.form, item.prop, personName.join(','));
        this.$set(this.form, item.propVal, personId.join(','));
        uni.removeStorageSync('personPageParams');
        uni.removeStorageSync('person_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
      });
      uni.navigateTo({
        url: `/pages/choose-person/choose-person?chooseType=${item.chooseType}&getListMode=${item.getListMode}`
      });
    },
    chooseDept(item) {
      let deptList = this.deptLabel[item.prop];
      uni.setStorageSync('dept_list', JSON.stringify(deptList));
      let deptPageParams = {
        title: item.name
      };
      if (item.getListType == 'scollSearch' || item.getListType == 'search') {
        let params = {};
        //获取查询参数
        for (var i = 0; i < item.searchParams.length; i++) {
          if (!this.form[item.searchParams[i].value]) {
            this.$u.toast(item.searchParams[i].message);
            return false;
          }
          params[item.searchParams[i].name] = this.form[
            item.searchParams[i].value
          ];
        }
        deptPageParams = { ...deptPageParams, params, api: item.searchApi };
      }
      uni.setStorageSync('deptPageParams', JSON.stringify(deptPageParams));
      //通过uni.$emit()和uni.$on() 进行页面间通讯
      uni.$on('trasenPerson', data => {
        this.$set(this.deptLabel, item.prop, data);
        let deptName = [],
          deptId = [];
        data.map(i => {
          deptName.push(i.name);
          deptId.push(i.userId);
        });
        this.$set(this.form, item.prop, deptName.join(','));
        this.$set(this.form, item.propVal, deptId.join(','));
        uni.removeStorageSync('deptPageParams');
        uni.removeStorageSync('dept_list');
        //清除监听，不清除会消耗资源
        uni.$off('trasenPerson');
      });
      uni.navigateTo({
        url: `/pages/choose-dept/choose-dept?chooseType=${item.chooseType}&getListMode=${item.getListMode}`
      });
    },
    //上传文件
    uploadedFile(data, item) {
      if (data[0].success && data[0].statusCode == 200) {
        let list = data[0].object.map(item => {
          return {
            url: item.filePath,
            fileUrl: item.filePath,
            fkFileId: item.fileId,
            fkFileName: item.fileRealName
          };
        });
        if (item.baseType != 'base') {
          this.form[data[3]].push(...list);
        } else {
          list[0].url = data[2][data[1]].url;
          this.form[`${item.prop}`].push(...list);
        }
      }
    },
    //删除文件
    removeFile(data, item) {
      if (item.baseType != 'base') {
        this.form[data[3]].splice(item.propVal, 1);
      } else {
        this.deletFile(data[0], item, {
          fileid: this.form[`${item.prop}`][data[0]]['fkFileId']
        });
      }
    },
    async deletFile(index, config, params) {
      await ajax
        .post(
          config.deletAction || '/ts-basics-bottom/fileAttachment/deleteFileId',
          params,
          {
            header: config.searchHeader || {}
          }
        )
        .then(res => {
          this.form[`${config.prop}`].splice(index, 1);
        });
    },
    //label右侧控件点击事件
    labelSlotClick(e) {
      if (e.labelSlotCallback) {
        e.labelSlotCallback(e);
      }
    },
    rightSlotClick(e) {
      if (e.labelSlotCallback) {
        e.rightSlotClick(e);
      }
    },
    validate() {
      let validVal = false;
      this.$refs.uForm.validate(valid => {
        validVal = valid;
      });
      return validVal;
    },
    //提交表单
    submit() {
      this.$refs.uForm.validate(valid => {
        if (valid) {
          this.$emit('submit', valid);
        }
      });
    },
    //删除附件
    deleteFile(prop, file) {
      this.$emit('deletFile', { file, prop });
    },
    previewFile(prop, file) {
      this.$emit('perviewFile', { file, prop });
    },
    inputClass(item) {
      let classList = [];
      if (item.type == 'select') {
        classList.push('is_select');
      }
      if (this.readOnly) {
        classList.push('is_readonly');
      }
      return classList.concat(item.class);
    }
  }
};
</script>

<style lang="scss" scoped>
.form-container {
  margin-top: $uni-spacing-col-base;
}
.form-box {
  padding: 0 $uni-spacing-row-lg;
  background-color: #ffffff;
}
.button-box {
  padding: $uni-spacing-col-base $uni-spacing-row-lg;
}
.is_readonly,
/deep/.is_select input {
  pointer-events: none;
}
.file-operation-button-item {
  margin-left: $uni-spacing-row-lg;
}
.file-list {
  display: flex;
  .u-icon {
    margin-right: 8px;
  }
  .file-list-item-title {
    max-width: 360rpx;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
