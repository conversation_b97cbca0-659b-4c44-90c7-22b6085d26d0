export default {
  data() {
    return {
      componentsList: [
        {
          components: 'doctorAdvice',
          label: '医嘱'
        },
        {
          components: 'checklist',
          label: '检查单'
        },
        {
          components: 'verify',
          label: '检验单'
        },
        {
          components: 'medicalRecord',
          label: '病历'
        },
        // {
        //   components: 'medicalInsurance',
        //   label: '医保'
        // },
        {
          components: 'cost',
          label: '费用'
        }
      ],
      colorList: []
    };
  },
  methods: {
    hasTypeColor(row) {
      let colorList = [];
      Object.keys(this.typeColor).forEach(i => {
        if (row[this.typeColor[i].key] == '1') colorList.push(i);
      });
      this.colorList = colorList;
    }
  }
};
