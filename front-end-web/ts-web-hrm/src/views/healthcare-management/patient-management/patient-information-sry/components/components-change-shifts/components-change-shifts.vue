<template>
  <div class="components-change-shifts">
    <el-tabs v-model="activeIndex" class="new-tabs-container">
      <el-tab-pane name="1" label="交接班信息"></el-tab-pane>
      <el-tab-pane name="2" label="指标信息"></el-tab-pane>
    </el-tabs>
    <change-shifts-information
      ref="changeShiftsInformation"
      v-if="activeIndex == '1'"
    />
    <Indicator-information
      ref="IndicatorInformation"
      v-if="activeIndex == '2'"
    />
  </div>
</template>

<script>
import changeShiftsInformation from './change-shifts-information.vue';
import IndicatorInformation from './Indicator-information.vue';
export default {
  components: { changeShiftsInformation, IndicatorInformation },
  data() {
    return {
      activeIndex: '1'
    };
  },
  watch: {
    activeIndex: {
      handler(val) {
        this.$nextTick(() => {
          let components =
            val == '1' ? 'changeShiftsInformation' : 'IndicatorInformation';
          this.$refs[components]?.refresh();
        });
      }
    }
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.components-change-shifts {
  flex: 1;
  display: flex;
  flex-direction: column;
}
</style>
