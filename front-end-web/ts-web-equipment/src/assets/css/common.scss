
* {
  margin: 0;
  padding: 0;
  font: 14px/1.4 "Microsoft Yahei", Arial, Helvetica, sans-serif;
  list-style-type: none;
  text-decoration: none;
  box-sizing: border-box;
  background-color: transparent;
}

.qiankun-child-container {
  width: 100%;
  height: 100%;
}

body, html, .qiankun-child-container, #app {
  width: 100%;
  height: 100%;
  background-color: transparent;
}

// flex设
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-space {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.flex-start {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}

.flex-end {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-row-center {
  display: flex;
  justify-content: center;
}

.flex-row-between {
  display: flex;
  justify-content: space-between;
}

.flex-col-center {
  display: flex;
  align-items: center;
}

.flex-row-evenly {
  display: flex;
  justify-content: space-evenly;
}

// ts-tabs样式不生效 进行修复
.el-tabs__nav {
  height: 36px !important;
}

.el-tabs__nav .el-tabs__item {
  font-weight: 400;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

// UI新规范
.ts-button {
  // 默认
  &.shallowButton {
    background-color: #d2dcfc !important;
    color: #295CF9 !important;
    border: none !important;
  }
  // 修改
  &.edit {
    background-color: #f59a23 !important;
    color: #fff !important;
    border: none !important;
  }
  // 删除
  &.del {
    background-color: #d9001b !important;
    color: #fff !important;
    border: none !important;
  }
  // 分享 绿色
  &.green {
    background-color: #449a00 !important;
    color: #fff !important;
    border: none !important;
  }
  // 默认色
  &.shall {
    background-color: #d2dcfc !important;
    color: #295CF9 !important;
    border: none !important;
  }
}

// 统一处理tabs样式
.new-tabs-container.el-tabs {
  .el-tabs__nav-wrap::after {
    background-color: #fff !important;
  }
  .el-tabs__header {
    border-bottom: none;
    margin-bottom: 2px;
  }
  .el-tabs__active-bar {
    background: #295CF9;
  }
  .el-tabs__item {
    background-color: #fff;
    border: none;
    border-radius: 0px;
    &.is-active {
      color: #295CF9;
    }
  }
}

// 金额输入框 文字向右对齐
.right-aligned-input {
  .el-input__inner {
    text-align: right;
  }
}
@import './vxe-table.scss';

// 更多搜索条件 图标变成更多
.trasen-search-content {
  .more-search-content {
    width: 58px;
    border: 1px solid $primary-blue;
    background: $primary-blue;
    .oa-icon-search_list {
      display: none;
    }
    .toast-content {
      color: #fff;
      &::before {
        content: '更多';
      }
    }
  }
}